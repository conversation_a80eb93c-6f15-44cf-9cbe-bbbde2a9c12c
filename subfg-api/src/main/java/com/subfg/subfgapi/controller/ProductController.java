package com.subfg.subfgapi.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.subfg.domain.entity.product.ProductPo;
import com.subfg.domain.request.ProductPageReq;
import com.subfg.domain.vo.Result;
import com.subfg.subfgapi.Serivce.ProductService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3/product")
@Tag(name = "产品管理", description = "产品管理相关接口")
public class ProductController {

    private final ProductService productService;


    /**
     * 分页获取产品列表
     */
    @GetMapping("/page")
    @Operation(summary = "分页获取产品列表", description = "分页获取产品列表")
    public Result<Page<ProductPo>> page(@RequestParam ProductPageReq req){
        return Result.success(productService.page(req));
    }
    
}
